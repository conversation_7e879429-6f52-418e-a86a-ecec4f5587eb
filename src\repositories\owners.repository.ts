import { RepositoryError, QueryError } from "../middlewares/errorHandler";
import { DatabaseErrorEnum as d } from "../enum/errors";
import { ResponseMessageEnum as m } from "../enum/response";
import { OwnerAttributes } from "../entities/owners.entity";
import { withMongoDB } from "../db/mongodb";

export class OwnerRepository {
    async findOwnerById(id: string): Promise<OwnerAttributes | null> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<OwnerAttributes>("owners").findOne(
                    {
                        id: id,
                        isDeleted: false,
                    },
                    { projection: { _id: 0 } }
                );

                if (!response) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findAll(): Promise<OwnerAttributes[]> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<OwnerAttributes>("owners").find({ isDeleted: false }).toArray();
                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findAllWithFilter(searchText: string): Promise<any> {
        try {
            return await withMongoDB(async (db) => {
                const query = {
                    isDeleted: false,
                    $or: [
                        { firstName: { $regex: `^${searchText}`, $options: "i" } },
                        {
                            $expr: {
                                // Converts the phoneNumber field (which is a number) into a string before applying the regex.
                                $regexMatch: {
                                    input: { $toString: "$phoneNumber" },
                                    regex: searchText,
                                    options: "i",
                                },
                            },
                        },
                    ],
                };

                const response = await db
                    .collection<OwnerAttributes>("owners")
                    .find(query, {
                        projection: {
                            _id: 0,
                            isDeleted: 0,
                            createdAt: 0,
                            updatedAt: 0,
                            deletedAt: 0,
                        },
                    })
                    .sort({ firstName: 1 })
                    .toArray();

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findList(limit: number, page: number): Promise<{ rows: OwnerAttributes[]; count: number }> {
        try {
            return await withMongoDB(async (db) => {
                const filter = { isDeleted: false };
                const offset = (page - 1) * limit;

                const count = await db.collection<OwnerAttributes>("owners").countDocuments(filter);

                const rows = await db
                    .collection<OwnerAttributes>("owners")
                    .find(filter)
                    .sort({ firstName: 1 })
                    .skip(offset)
                    .limit(limit)
                    .toArray();
                return { rows, count };
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async findByPhoneNumber(phoneNumber: number): Promise<OwnerAttributes | null> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<OwnerAttributes>("owners").findOne(
                    {
                        "ownerInfo.phoneNumber": phoneNumber,
                        isDeleted: false,
                    },
                    { projection: { _id: 0 } }
                );

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async create(data: OwnerAttributes): Promise<OwnerAttributes> {
        try {
            return await withMongoDB(async (db) => {
                const ownerData: OwnerAttributes = {
                    ...data,
                    isDeleted: false,
                    createdAt: new Date(),
                    updatedAt: new Date(),
                    deletedAt: null,
                };
                const createdOwner = await db.collection<OwnerAttributes>("owners").insertOne(ownerData);

                if (!createdOwner) {
                    throw new QueryError(m.INSERTION_FAILED);
                }

                const response = await db
                    .collection<OwnerAttributes>("owners")
                    .findOne({ id: ownerData.id }, { projection: { _id: 0 } });

                if (!response) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async update(id: string, data: Partial<OwnerAttributes>): Promise<boolean> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<OwnerAttributes>("owners").updateOne(
                    { id: id, isDeleted: false },
                    {
                        $set: {
                            ...data,
                            updatedAt: new Date(),
                        },
                    }
                );
                if (response.matchedCount === 0) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);
                return response.modifiedCount === 1;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }

    async delete(id: string): Promise<boolean> {
        try {
            return await withMongoDB(async (db) => {
                const response = await db.collection<OwnerAttributes>("owners").updateOne(
                    {
                        id: id,
                        isDeleted: false,
                    },
                    {
                        $set: {
                            isDeleted: true,
                            deletedAt: new Date(),
                        },
                    }
                );

                if (response.matchedCount === 0) throw new QueryError(m.ID_NOT_FOUND, d.RECORD_NOT_FOUND);

                return response.modifiedCount > 0;
            });
        } catch (error) {
            if (error instanceof QueryError) {
                throw new RepositoryError(error.message, error.errors);
            }
            throw new RepositoryError(m.DATABASE_ERROR, d.DATABASE_TIMEOUT);
        }
    }
}

export default new OwnerRepository();
