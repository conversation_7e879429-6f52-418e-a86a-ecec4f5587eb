import express, { NextFunction, Request, Response, Router } from "express";
import { ResponseTitleEnum as r, ResponseStatusCodeEnum as s, ResponseMessageEnum as m } from "../enum/response";
import { VisitModule } from "../modules/visits/visits.module";
import {
    VisitRequest,
    PetInfoSubmitRequest,
    PetHealthSubmitRequest,
    PetRequirementSubmitRequest,
    PetNutritionSubmitRequest,
    PetProductMatchSubmitRequest,
    OrderSummarySubmitRequest,
} from "../modules/visits/dto/request";
import { ListLimitConstants } from "../constants/defaultValue";

export class VisitController {
    private readonly visitModule: VisitModule;
    public router: Router;
    constructor(visitModule: VisitModule) {
        this.visitModule = visitModule;
        this.router = express.Router();
        this.routes();
    }
    private routes() {
        this.router.get("/list", this.getList.bind(this));
        this.router.get("/personal/nutrients/:id", this.getPersonalizedNutrients.bind(this));
        this.router.get("/:id", this.getOne.bind(this));
        this.router.get("/", this.getAll.bind(this));
        this.router.post("/step/info", this.submitPetInfo.bind(this));
        this.router.post("/step/health", this.submitPetHealth.bind(this));
        this.router.post("/step/requirement", this.submitPetRequirement.bind(this));
        this.router.post("/step/nutrition", this.submitPetNutrition.bind(this));
        this.router.post("/step/product", this.submitPetProductMatch.bind(this));
        this.router.post("/step/order", this.submitPetOrder.bind(this));
        this.router.post("/", this.create.bind(this));
        this.router.put("/:id", this.update.bind(this));
        this.router.delete("/:id", this.delete.bind(this));
    }

    private async submitPetInfo(req: Request, res: Response, next: NextFunction) {
        try {
            const body = req.body as PetInfoSubmitRequest;
            const response = await this.visitModule.handlePetInfoStep(body);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async submitPetHealth(req: Request, res: Response, next: NextFunction) {
        try {
            const body = req.body as PetHealthSubmitRequest;
            const response = await this.visitModule.handlePetHealthStep(body);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async submitPetRequirement(req: Request, res: Response, next: NextFunction) {
        try {
            const body = req.body as PetRequirementSubmitRequest;
            const response = await this.visitModule.handlePetRequirementStep(body);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async submitPetNutrition(req: Request, res: Response, next: NextFunction) {
        try {
            const body = req.body as PetNutritionSubmitRequest;
            const response = await this.visitModule.handlePetNutritionStep(body);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async submitPetProductMatch(req: Request, res: Response, next: NextFunction) {
        try {
            const body = req.body as PetProductMatchSubmitRequest;
            const response = await this.visitModule.handlePetProductMatchStep(body);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async submitPetOrder(req: Request, res: Response, next: NextFunction) {
        try {
            const body = req.body as OrderSummarySubmitRequest;
            const response = await this.visitModule.handleOrderSummaryStep(body);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async getPersonalizedNutrients(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const response = await this.visitModule.getPersonalizedNutrients(id);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async getOne(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const response = await this.visitModule.getOne(id);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async getAll(req: Request, res: Response, next: NextFunction) {
        try {
            const response = await this.visitModule.getAll();
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async getList(req: Request, res: Response, next: NextFunction) {
        try {
            // const limit = Number(req.query.limit) || ListLimitConstants.DEFAULT_LIMIT;
            // const page = Number(req.query.page) || ListLimitConstants.DEFAULT_PAGE;
            const filter: any = {
                petHnId: req.query.petHnId as string,
                petName: req.query.petName as string,
                ownerName: req.query.ownerName as string,
                doctorId: req.query.doctorId as string,
                dateFrom: req.query.dateFrom as string,
                dateTo: req.query.dateTo as string,
                limit: Number(req.query.limit) || ListLimitConstants.DEFAULT_LIMIT,
                page: Number(req.query.page) || ListLimitConstants.DEFAULT_PAGE,
            }

            const response = await this.visitModule.getList(filter);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async create(req: Request, res: Response, next: NextFunction) {
        try {
            const body = req.body as VisitRequest;
            const response = await this.visitModule.create(body);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }

    private async update(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const data = req.body;
            const response = await this.visitModule.update(id, data);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
    private async delete(req: Request, res: Response, next: NextFunction) {
        try {
            const id = req.params.id;
            const response = await this.visitModule.delete(id);
            res.responseJson(s.SUCCESS, r.SUCCESS, m.SUCCESS, response, null);
        } catch (error) {
            next(error);
        }
    }
}
