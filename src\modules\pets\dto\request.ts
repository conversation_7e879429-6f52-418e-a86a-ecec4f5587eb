import { PetInfo } from "../../../entities/pets.entity";
import { OwnerInfo } from "../../../entities/owners.entity";

export type PetRequest = {
    ownerId: string | null;
    petInfo: PetInfo;
};

export type CreatePetWithOwnerRequest = {
    petDetails: {
        ownerId: string | null;
        petInfo: PetInfo;
    };
    ownerDetails?: {
        ownerInfo: OwnerInfo;
    };
};

export type CalculateBCSScoreRequest = {
    scores: number[];
}
